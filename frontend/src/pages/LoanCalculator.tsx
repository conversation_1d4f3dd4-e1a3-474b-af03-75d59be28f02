import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Form,
  FormGroup,
  Input,
  Select,
  Button,
  Alert
} from '../components/ui';
import { LoanCalculationData, LoanCalculationResult, RepaymentMethod } from '../types';
import LoanService from '../services/loanService';

export const LoanCalculator: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [calculationResult, setCalculationResult] = useState<LoanCalculationResult | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm<LoanCalculationData>({
    defaultValues: {
      interestRate: 10,
      advancePayment: 0
    }
  });

  const onSubmit = async (data: LoanCalculationData) => {
    try {
      setLoading(true);
      setError(null);

      const calculationData = {
        ...data,
        loanAmount: Number(data.loanAmount),
        repaymentDuration: Number(data.repaymentDuration),
        repaymentMethod: data.repaymentMethod as RepaymentMethod,
        advancePayment: Number(data.advancePayment || 0),
        interestRate: Number(data.interestRate || 10)
      };

      const result = await LoanService.calculateLoan(calculationData);
      setCalculationResult(result);
    } catch (err: any) {
      console.error('Error calculating loan:', err);
      setError(err.response?.data?.message || 'Failed to calculate loan. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/dashboard');
  };

  const formatCurrency = (amount: number) => {
    return `৳${amount.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-BD');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Loan Calculator
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Calculate loan installments and repayment schedule
          </p>
        </div>
        <button
          onClick={handleCancel}
          className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
        >
          ← Back to Dashboard
        </button>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert 
          type="error" 
          title="Error" 
          description={error}
          onClose={() => setError(null)}
        />
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Calculator Form */}
        <Card>
          <CardHeader>
            <CardTitle>Loan Details</CardTitle>
          </CardHeader>
          <CardContent>
            <Form onSubmit={handleSubmit(onSubmit)} spacing="lg">
              <Input
                label="Loan Amount (৳)"
                type="number"
                step="100"
                min="1000"
                {...register('loanAmount', { 
                  required: 'Loan amount is required',
                  min: { value: 1000, message: 'Minimum loan amount is ৳1,000' },
                  max: { value: 10000000, message: 'Maximum loan amount is ৳1,00,00,000' }
                })}
                error={errors.loanAmount?.message}
                required
              />

              <Input
                label="Repayment Duration (Months)"
                type="number"
                min="1"
                max="60"
                {...register('repaymentDuration', { 
                  required: 'Repayment duration is required',
                  min: { value: 1, message: 'Minimum duration is 1 month' },
                  max: { value: 60, message: 'Maximum duration is 60 months' }
                })}
                error={errors.repaymentDuration?.message}
                required
              />

              <Select
                label="Repayment Method"
                {...register('repaymentMethod', { required: 'Repayment method is required' })}
                error={errors.repaymentMethod?.message}
                required
              >
                <option value="">Select repayment method</option>
                <option value="WEEKLY">Weekly</option>
                <option value="MONTHLY">Monthly</option>
                <option value="QUARTERLY">Quarterly</option>
              </Select>

              <Input
                label="Interest Rate (%)"
                type="number"
                step="0.1"
                min="0"
                max="50"
                {...register('interestRate')}
                error={errors.interestRate?.message}
              />

              <Input
                label="Advance Payment (৳)"
                type="number"
                step="100"
                min="0"
                {...register('advancePayment')}
                error={errors.advancePayment?.message}
              />

              <Button
                type="submit"
                variant="primary"
                disabled={loading}
                fullWidth
              >
                {loading ? 'Calculating...' : 'Calculate Loan'}
              </Button>
            </Form>
          </CardContent>
        </Card>

        {/* Calculation Results */}
        <Card>
          <CardHeader>
            <CardTitle>Calculation Results</CardTitle>
          </CardHeader>
          <CardContent>
            {!calculationResult ? (
              <div className="text-center py-12 text-gray-500 dark:text-gray-400">
                <div className="text-4xl mb-4">🧮</div>
                <p>Enter loan details and click "Calculate Loan" to see results</p>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Summary Cards */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <p className="text-sm text-blue-600 dark:text-blue-400">Loan Amount</p>
                    <p className="text-xl font-bold text-blue-900 dark:text-blue-100">
                      {formatCurrency(calculationResult.loanAmount)}
                    </p>
                  </div>
                  <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <p className="text-sm text-green-600 dark:text-green-400">Effective Amount</p>
                    <p className="text-xl font-bold text-green-900 dark:text-green-100">
                      {formatCurrency(calculationResult.effectiveAmount)}
                    </p>
                  </div>
                  <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <p className="text-sm text-purple-600 dark:text-purple-400">Installment Amount</p>
                    <p className="text-xl font-bold text-purple-900 dark:text-purple-100">
                      {formatCurrency(calculationResult.installmentAmount)}
                    </p>
                  </div>
                  <div className="p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                    <p className="text-sm text-orange-600 dark:text-orange-400">Total Repayment</p>
                    <p className="text-xl font-bold text-orange-900 dark:text-orange-100">
                      {formatCurrency(calculationResult.totalRepaymentAmount)}
                    </p>
                  </div>
                </div>

                {/* Detailed Information */}
                <div className="space-y-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Advance Payment:</span>
                    <span className="font-medium">{formatCurrency(calculationResult.advancePayment)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Interest Amount:</span>
                    <span className="font-medium">{formatCurrency(calculationResult.interestAmount)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Number of Installments:</span>
                    <span className="font-medium">{calculationResult.installmentCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">First Installment Date:</span>
                    <span className="font-medium">{formatDate(calculationResult.firstInstallmentDate)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Last Installment Date:</span>
                    <span className="font-medium">{formatDate(calculationResult.lastInstallmentDate)}</span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                  <Button
                    variant="primary"
                    onClick={() => navigate('/loans/apply', { 
                      state: { calculationResult } 
                    })}
                    fullWidth
                  >
                    Apply for This Loan
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default LoanCalculator;
