import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Form,
  FormGroup,
  Input,
  Select,
  Textarea,
  Button,
  Alert,
  Loading
} from '../components/ui';
import { LoanApplication } from '../types';
import { useAuth } from '../contexts/AuthContext';
import LoanService from '../services/loanService';

interface ApprovalFormData {
  decision: 'approve' | 'reject';
  approvedAmount?: number;
  rejectionReason?: string;
  notes?: string;
}

export const LoanApproval: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [applications, setApplications] = useState<LoanApplication[]>([]);
  const [selectedApplication, setSelectedApplication] = useState<LoanApplication | null>(null);
  const [loadingApplications, setLoadingApplications] = useState(true);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset
  } = useForm<ApprovalFormData>();

  const decision = watch('decision');

  useEffect(() => {
    fetchPendingApplications();
  }, []);

  const fetchPendingApplications = async () => {
    try {
      setLoadingApplications(true);
      const response = await LoanService.getLoanApplications({
        status: 'pending'
      });
      setApplications(response.data);
    } catch (err) {
      console.error('Error fetching applications:', err);
      setError('Failed to load loan applications.');
    } finally {
      setLoadingApplications(false);
    }
  };

  const handleApplicationSelect = (application: LoanApplication) => {
    setSelectedApplication(application);
    reset();
    setValue('approvedAmount', application.loanAmount);
  };

  const onSubmit = async (data: ApprovalFormData) => {
    if (!selectedApplication) {
      setError('Please select an application to review.');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      if (data.decision === 'approve') {
        await LoanService.approveLoanApplication(selectedApplication.id, {
          approvedAmount: Number(data.approvedAmount),
          notes: data.notes
        });
        setSuccess(`Loan application approved for ৳${data.approvedAmount?.toLocaleString()}!`);
      } else {
        await LoanService.rejectLoanApplication(selectedApplication.id, {
          rejectionReason: data.rejectionReason || '',
          notes: data.notes
        });
        setSuccess('Loan application rejected.');
      }

      // Refresh applications
      await fetchPendingApplications();
      setSelectedApplication(null);
      reset();

      setTimeout(() => {
        navigate('/dashboard', {
          state: {
            message: `Loan application ${data.decision === 'approve' ? 'approved' : 'rejected'} successfully!`,
            type: 'success'
          }
        });
      }, 2000);
    } catch (err: any) {
      console.error('Error processing application:', err);
      setError(err.response?.data?.message || 'Failed to process application. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/dashboard');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-BD');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'approved':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };

  if (loadingApplications) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <Loading size="lg" />
          <p className="text-gray-600 dark:text-gray-400 mt-4">Loading loan applications...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Loan Approval Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Review and approve/reject loan applications
          </p>
        </div>
        <button
          onClick={handleCancel}
          className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
        >
          ← Back to Dashboard
        </button>
      </div>

      {/* Success Alert */}
      {success && (
        <Alert 
          type="success" 
          title="Success" 
          description={success}
        />
      )}

      {/* Error Alert */}
      {error && (
        <Alert 
          type="error" 
          title="Error" 
          description={error}
          onClose={() => setError(null)}
        />
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Pending Applications */}
        <Card>
          <CardHeader>
            <CardTitle>Pending Applications ({applications.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {applications.length === 0 ? (
              <div className="text-center py-12 text-gray-500 dark:text-gray-400">
                <div className="text-4xl mb-4">✅</div>
                <p>No pending loan applications</p>
              </div>
            ) : (
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {applications.map((application) => (
                  <div
                    key={application.id}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedApplication?.id === application.id
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                    }`}
                    onClick={() => handleApplicationSelect(application)}
                  >
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {application.memberName}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          ID: {application.memberMemberId}
                        </p>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(application.status)}`}>
                        {application.status}
                      </span>
                    </div>
                    <div className="space-y-1">
                      <p className="text-lg font-semibold text-gray-900 dark:text-white">
                        ৳{application.loanAmount.toLocaleString()}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Purpose: {application.purpose}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Applied: {formatDate(application.appliedAt)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Application Review */}
        <Card>
          <CardHeader>
            <CardTitle>Application Review</CardTitle>
          </CardHeader>
          <CardContent>
            {!selectedApplication ? (
              <div className="text-center py-12 text-gray-500 dark:text-gray-400">
                <div className="text-4xl mb-4">📋</div>
                <p>Select an application to review</p>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Application Details */}
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                    Application Details
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Member:</span>
                      <span>{selectedApplication.memberName}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Member ID:</span>
                      <span>{selectedApplication.memberMemberId}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Requested Amount:</span>
                      <span className="font-medium">৳{selectedApplication.loanAmount.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Purpose:</span>
                      <span>{selectedApplication.purpose}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Applied Date:</span>
                      <span>{formatDate(selectedApplication.appliedAt)}</span>
                    </div>
                    {selectedApplication.guarantorName && (
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Guarantor:</span>
                        <span>{selectedApplication.guarantorName}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Review Form */}
                <Form onSubmit={handleSubmit(onSubmit)} spacing="lg">
                  <Select
                    label="Decision"
                    {...register('decision', { required: 'Decision is required' })}
                    error={errors.decision?.message}
                    required
                  >
                    <option value="">Select decision</option>
                    <option value="approve">Approve</option>
                    <option value="reject">Reject</option>
                  </Select>

                  {decision === 'approve' && (
                    <Input
                      label="Approved Amount (৳)"
                      type="number"
                      step="100"
                      min="1000"
                      {...register('approvedAmount', { 
                        required: decision === 'approve' ? 'Approved amount is required' : false,
                        min: { value: 1000, message: 'Minimum amount is ৳1,000' }
                      })}
                      error={errors.approvedAmount?.message}
                      required={decision === 'approve'}
                    />
                  )}

                  {decision === 'reject' && (
                    <Textarea
                      label="Rejection Reason"
                      rows={3}
                      {...register('rejectionReason', { 
                        required: decision === 'reject' ? 'Rejection reason is required' : false
                      })}
                      error={errors.rejectionReason?.message}
                      placeholder="Explain why the application is being rejected..."
                      required={decision === 'reject'}
                    />
                  )}

                  <Textarea
                    label="Additional Notes (Optional)"
                    rows={3}
                    {...register('notes')}
                    placeholder="Add any additional notes..."
                  />

                  <div className="flex gap-4 pt-4">
                    <Button
                      type="submit"
                      variant={decision === 'approve' ? 'primary' : 'danger'}
                      disabled={loading || !decision}
                      className="flex-1"
                    >
                      {loading ? 'Processing...' : decision === 'approve' ? 'Approve Application' : 'Reject Application'}
                    </Button>
                    <Button
                      type="button"
                      variant="secondary"
                      onClick={() => setSelectedApplication(null)}
                      disabled={loading}
                    >
                      Cancel
                    </Button>
                  </div>
                </Form>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default LoanApproval;
