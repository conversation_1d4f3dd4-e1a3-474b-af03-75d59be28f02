import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Form,
  FormGroup,
  Input,
  Select,
  Textarea,
  Button,
  Alert
} from '../components/ui';
import { MemberSearch } from '../components/members';
import { CreateLoanApplicationData, MemberSearchResult, Branch } from '../types';
import { useAuth } from '../contexts/AuthContext';
import LoanService from '../services/loanService';
import BranchService from '../services/branchService';

export const LoanApplication: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedMember, setSelectedMember] = useState<MemberSearchResult | null>(null);
  const [branches, setBranches] = useState<Branch[]>([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset
  } = useForm<CreateLoanApplicationData>();

  useEffect(() => {
    const fetchBranches = async () => {
      try {
        const response = await BranchService.getBranches();
        setBranches(response.data);
      } catch (err) {
        console.error('Error fetching branches:', err);
      }
    };

    fetchBranches();
  }, []);

  const onSubmit = async (data: CreateLoanApplicationData) => {
    if (!selectedMember) {
      setError('Please select a member for the loan application.');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const applicationData = {
        ...data,
        memberId: selectedMember.id,
        loanAmount: Number(data.loanAmount)
      };

      await LoanService.createLoanApplication(applicationData);
      setSuccess('Loan application submitted successfully!');
      
      setTimeout(() => {
        navigate('/dashboard', {
          state: {
            message: 'Loan application submitted successfully!',
            type: 'success'
          }
        });
      }, 2000);
    } catch (err: any) {
      console.error('Error creating loan application:', err);
      setError(err.response?.data?.message || 'Failed to submit loan application. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/dashboard');
  };

  const handleMemberSelect = (member: MemberSearchResult) => {
    setSelectedMember(member);
    setError(null);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Loan Application
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Submit a new loan application
          </p>
        </div>
        <button
          onClick={handleCancel}
          className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
        >
          ← Back to Dashboard
        </button>
      </div>

      {/* Success Alert */}
      {success && (
        <Alert 
          type="success" 
          title="Success" 
          description={success}
        />
      )}

      {/* Error Alert */}
      {error && (
        <Alert 
          type="error" 
          title="Error" 
          description={error}
          onClose={() => setError(null)}
        />
      )}

      {/* Application Form */}
      <Card>
        <CardHeader>
          <CardTitle>Loan Application Details</CardTitle>
        </CardHeader>
        <CardContent>
          <Form onSubmit={handleSubmit(onSubmit)} spacing="lg">
            {/* Member Selection */}
            <FormGroup>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Select Member *
              </label>
              <MemberSearch
                onMemberSelect={handleMemberSelect}
                selectedMember={selectedMember}
                placeholder="Search for member by name or ID..."
                branches={branches}
              />
              {selectedMember && (
                <div className="mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <p className="text-sm text-green-700 dark:text-green-300">
                    Selected: {selectedMember.name} (ID: {selectedMember.memberId})
                  </p>
                </div>
              )}
            </FormGroup>

            {/* Loan Amount */}
            <Input
              label="Loan Amount (৳)"
              type="number"
              step="100"
              min="1000"
              {...register('loanAmount', { 
                required: 'Loan amount is required',
                min: { value: 1000, message: 'Minimum loan amount is ৳1,000' },
                max: { value: 1000000, message: 'Maximum loan amount is ৳10,00,000' }
              })}
              error={errors.loanAmount?.message}
              required
            />

            {/* Purpose */}
            <Textarea
              label="Purpose of Loan"
              rows={3}
              {...register('purpose', { 
                required: 'Purpose is required',
                minLength: { value: 10, message: 'Purpose must be at least 10 characters' }
              })}
              error={errors.purpose?.message}
              placeholder="Describe the purpose of the loan..."
              required
            />

            {/* Guarantor (Optional) */}
            <FormGroup>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Guarantor (Optional)
              </label>
              <MemberSearch
                onMemberSelect={(guarantor) => setValue('guarantorId', guarantor.id)}
                placeholder="Search for guarantor by name or ID..."
                branches={branches}
              />
            </FormGroup>

            {/* Action Buttons */}
            <div className="flex gap-4 pt-4">
              <Button
                type="submit"
                variant="primary"
                disabled={loading || !selectedMember}
                className="flex-1"
              >
                {loading ? 'Submitting...' : 'Submit Application'}
              </Button>
              <Button
                type="button"
                variant="secondary"
                onClick={handleCancel}
                disabled={loading}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

export default LoanApplication;
