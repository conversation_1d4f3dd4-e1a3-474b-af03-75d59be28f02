import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Form,
  FormGroup,
  Input,
  Select,
  Textarea,
  Button,
  Alert,
  Loading
} from '../components/ui';
import { MemberSearch } from '../components/members';
import { CollectInstallmentData, MemberSearchResult, Installment } from '../types';
import { useAuth } from '../contexts/AuthContext';
import InstallmentService from '../services/installmentService';

export const InstallmentCollection: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedMember, setSelectedMember] = useState<MemberSearchResult | null>(null);
  const [pendingInstallments, setPendingInstallments] = useState<Installment[]>([]);
  const [selectedInstallment, setSelectedInstallment] = useState<Installment | null>(null);
  const [loadingInstallments, setLoadingInstallments] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset
  } = useForm<CollectInstallmentData>();

  const fetchMemberInstallments = async (memberId: string) => {
    try {
      setLoadingInstallments(true);
      const response = await InstallmentService.getInstallments({
        memberId,
        status: 'pending'
      });
      setPendingInstallments(response.data);
    } catch (err) {
      console.error('Error fetching installments:', err);
      setError('Failed to load member installments.');
    } finally {
      setLoadingInstallments(false);
    }
  };

  const handleMemberSelect = (member: MemberSearchResult) => {
    setSelectedMember(member);
    setSelectedInstallment(null);
    setPendingInstallments([]);
    setError(null);
    fetchMemberInstallments(member.id);
  };

  const handleInstallmentSelect = (installment: Installment) => {
    setSelectedInstallment(installment);
    setValue('amount', installment.amount);
  };

  const onSubmit = async (data: CollectInstallmentData) => {
    if (!selectedInstallment) {
      setError('Please select an installment to collect.');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      await InstallmentService.collectInstallment(selectedInstallment.id, {
        ...data,
        amount: Number(data.amount)
      });

      setSuccess(`Installment of ৳${data.amount} collected successfully!`);
      
      // Refresh installments
      if (selectedMember) {
        await fetchMemberInstallments(selectedMember.id);
      }
      
      // Reset form
      reset();
      setSelectedInstallment(null);

      setTimeout(() => {
        navigate('/dashboard', {
          state: {
            message: `Installment collected successfully from ${selectedMember?.name}!`,
            type: 'success'
          }
        });
      }, 2000);
    } catch (err: any) {
      console.error('Error collecting installment:', err);
      setError(err.response?.data?.message || 'Failed to collect installment. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/dashboard');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-BD');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'overdue':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Installment Collection
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Collect loan installments from members
          </p>
        </div>
        <button
          onClick={handleCancel}
          className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
        >
          ← Back to Dashboard
        </button>
      </div>

      {/* Success Alert */}
      {success && (
        <Alert 
          type="success" 
          title="Success" 
          description={success}
        />
      )}

      {/* Error Alert */}
      {error && (
        <Alert 
          type="error" 
          title="Error" 
          description={error}
          onClose={() => setError(null)}
        />
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Member Selection */}
        <Card>
          <CardHeader>
            <CardTitle>Select Member</CardTitle>
          </CardHeader>
          <CardContent>
            <MemberSearch
              onMemberSelect={handleMemberSelect}
              selectedMember={selectedMember}
              placeholder="Search for member by name or ID..."
            />
            
            {selectedMember && (
              <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <h4 className="font-medium text-blue-900 dark:text-blue-100">
                  {selectedMember.name}
                </h4>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Member ID: {selectedMember.memberId}
                </p>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Phone: {selectedMember.phone}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pending Installments */}
        <Card>
          <CardHeader>
            <CardTitle>Pending Installments</CardTitle>
          </CardHeader>
          <CardContent>
            {!selectedMember ? (
              <p className="text-gray-500 dark:text-gray-400 text-center py-8">
                Select a member to view pending installments
              </p>
            ) : loadingInstallments ? (
              <div className="text-center py-8">
                <Loading size="sm" />
                <p className="text-gray-500 dark:text-gray-400 mt-2">Loading installments...</p>
              </div>
            ) : pendingInstallments.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 text-center py-8">
                No pending installments found
              </p>
            ) : (
              <div className="space-y-3">
                {pendingInstallments.map((installment) => (
                  <div
                    key={installment.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedInstallment?.id === installment.id
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                    }`}
                    onClick={() => handleInstallmentSelect(installment)}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="font-medium">৳{installment.amount.toLocaleString()}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Due: {formatDate(installment.dueDate)}
                        </p>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(installment.status)}`}>
                        {installment.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Collection Form */}
      {selectedInstallment && (
        <Card>
          <CardHeader>
            <CardTitle>Collect Installment</CardTitle>
          </CardHeader>
          <CardContent>
            <Form onSubmit={handleSubmit(onSubmit)} spacing="lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Collection Amount (৳)"
                  type="number"
                  step="0.01"
                  {...register('amount', { 
                    required: 'Amount is required',
                    min: { value: 0.01, message: 'Amount must be greater than 0' }
                  })}
                  error={errors.amount?.message}
                  required
                />

                <Select
                  label="Payment Method"
                  {...register('paymentMethod', { required: 'Payment method is required' })}
                  error={errors.paymentMethod?.message}
                  required
                >
                  <option value="">Select payment method</option>
                  <option value="cash">Cash</option>
                  <option value="bank_transfer">Bank Transfer</option>
                  <option value="mobile_banking">Mobile Banking</option>
                </Select>
              </div>

              <Input
                label="Transaction Reference (Optional)"
                {...register('transactionRef')}
                placeholder="Enter transaction reference number"
              />

              <Textarea
                label="Notes (Optional)"
                rows={3}
                {...register('notes')}
                placeholder="Add any additional notes..."
              />

              <div className="flex gap-4 pt-4">
                <Button
                  type="submit"
                  variant="primary"
                  disabled={loading}
                  className="flex-1"
                >
                  {loading ? 'Collecting...' : 'Collect Installment'}
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => setSelectedInstallment(null)}
                  disabled={loading}
                >
                  Cancel
                </Button>
              </div>
            </Form>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default InstallmentCollection;
